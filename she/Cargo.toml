[package]
name = "she"
version = "1.0.14"
edition = "2021"

[profile.release]
opt-level = 3        # 最高级别的优化
lto = true           # 启用链接时间优化
codegen-units = 1    # 单一代码生成单元以提高优化效果
debug = false        # 不包含调试信息
rpath = false        # 禁用运行时搜索路径
panic = 'abort'      # 使用 abort 而不是 unwind 的 panic 策略
incremental = false  # 禁用增量编译

[dependencies]
tokio = { version = "1.47.0", features = ["full"] }
log = "0.4.27"
flexi_logger = "0.31.2"
once_cell = "1.21.3"
thiserror = "2.0.12"
config = "0.15.13"
reqwest = { version = "0.12.22", default-features = false, features = ["json", "rustls-tls"] }
url = "2.5.4"
serde = "1.0.219"
validator = { version = "0.20.0", features = ["derive"] }
sysinfo = "0.36.1"
chrono = "0.4.41"
tabled = { version = "0.20.0", features = ["std", "derive", "ansi"] }
nix = { version = "0.30.1", features = ["user"] }